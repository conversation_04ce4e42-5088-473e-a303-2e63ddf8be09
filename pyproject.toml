[project]
name = "runpod-exp-setup"
version = "0.1.0"
description = "Runpod Experiment Setup"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "torch>=2.7.0",
]

[project.optional-dependencies]
cpu = [
  "torch>=2.7.0",
]
cuda = [
  "torch>=2.7.0",
]

[tool.uv]
conflicts = [
  [
    { extra = "cpu" },
    { extra = "cuda" },
  ],
]

[tool.uv.sources]
torch = [
  { index = "pytorch-cpu", extra = "cpu" },
  { index = "pytorch-cuda", extra = "cuda" },
]

[[tool.uv.index]]
name = "pytorch-cpu"
url = "https://download.pytorch.org/whl/cpu"
explicit = true

[[tool.uv.index]]
name = "pytorch-cuda"
url = "https://download.pytorch.org/whl/cu128"
explicit = true