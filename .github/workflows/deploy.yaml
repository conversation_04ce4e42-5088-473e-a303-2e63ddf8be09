name: Deploy to RunPod

on:
  workflow_dispatch:
    inputs:
      config_file:
        description: 'RunPod configuration file name'
        required: false
        default: 'default.json'
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Create RunPod Pod
        id: create_pod
        run: |
          # Read the pod configuration from the file
          POD_CONFIG=$(cat runpod_configs/${{ inputs.config_file }} | jq -c .)

          # Construct the full JSON payload for the RunPod API
          JSON_PAYLOAD=$(jq -n --argjson config "$POD_CONFIG" --arg repo_url "**************:${{ github.repository }}.git" \
            '{
              "name": $config.name,
              "allowedCudaVersions": $config.allowedCudaVersions,
              "cloudType": "SECURE",
              "imageName": $config.imageName,
              "gpuCount": $config.gpuCount,
              "gpuTypeIds": $config.gpuTypeIds,
              "computeType": $config.computeType,
              "minRAMPerGPU": $config.minRAMPerGPU,
              "minVCPUPerGPU": $config.minVCPUPerGPU,
              "containerDiskInGb": $config.containerDiskInGb,
              "volumeInGb": $config.volumeInGb,
              "vcpuCount": $config.vcpuCount,
              "ports": $config.ports,
              "volumeMountPath": $config.volumeMountPath,
              "supportPublicIp": true,
              "env": {
                "GIT_REPO_URL": $repo_url
              }
            }')

          # Make the API call and capture both response and status code
          response_and_code=$(curl -s -w "\n%{http_code}" -X POST \
            -H "Authorization: Bearer ${{ secrets.RUNPOD_API_KEY }}" \
            -H "Content-Type: application/json" \
            -d "$JSON_PAYLOAD" \
            https://rest.runpod.io/v1/pods)

          # Split response and status code
          response=$(echo "$response_and_code" | head -n 1)
          status_code=$(echo "$response_and_code" | tail -n 1)

          # Check if status code indicates success (201 Created)
          if [ "$status_code" -ne 201 ]; then
            echo "Error: RunPod API request failed with status code $status_code"
            echo "Response: $response"
            exit 1
          fi

          # Extract pod ID for subsequent steps
          echo "POD_RESPONSE: $response"
          pod_id=$(echo $response | jq -r '.id')

          # Verify pod ID is present
          if [ -z "$pod_id" ] || [ "$pod_id" = "null" ]; then
            echo "Error: Failed to get pod ID from API response"
            echo "Response: $response"
            exit 1
          fi

          echo "pod_id=$pod_id" >> $GITHUB_OUTPUT

      - name: Get Pod IP Address
        id: get_pod_ip
        run: |
          POD_ID=${{ steps.create_pod.outputs.pod_id }}
          echo "Waiting for pod $POD_ID to get IP address..."
          sleep 15
          
          # Retry logic to get IP address
          for i in {1..10}; do
            echo "Attempt $i: Getting pod details..."

            # Get pod details
            response=$(curl -s -X GET \
              -H "Authorization: Bearer ${{ secrets.RUNPOD_API_KEY }}" \
              -H "Content-Type: application/json" \
              https://rest.runpod.io/v1/pods/$POD_ID)

            # Extract IP address and SSH port
            pod_ip=$(echo $response | jq -r '.publicIp')
            ssh_port=$(echo $response | jq -r '.portMappings."22"')

            # Check if IP is available and not null
            if [ -n "$pod_ip" ] && [ "$pod_ip" != "null" ] && [ -n "$ssh_port" ] && [ "$ssh_port" != "null" ]; then
              echo "Pod IP address found: $pod_ip"
              echo "SSH port found: $ssh_port"
              echo "pod_ip=$pod_ip" >> $GITHUB_OUTPUT
              echo "ssh_port=$ssh_port" >> $GITHUB_OUTPUT
              exit 0
            fi

            echo "IP address or SSH port not yet available (IP: $pod_ip, SSH port: $ssh_port), retrying in 15 seconds..."
            sleep 15
          done

          echo "Error: Failed to get pod IP address and SSH port after 10 attempts"
          echo "Last response: $response"
          exit 1


      - name: Wait for SSH to be ready
        run: |
          IP=${{ steps.get_pod_ip.outputs.pod_ip }}
          SSH_PORT=${{ steps.get_pod_ip.outputs.ssh_port }}
          echo "Waiting for SSH on $IP:$SSH_PORT..."
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_ed25519
          chmod 600 ~/.ssh/id_ed25519
          for i in {1..10}; do
            if ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 -p $SSH_PORT root@$IP -i ~/.ssh/id_ed25519 exit; then
              echo "SSH is ready!"
              exit 0
            fi
            echo "Attempt $i failed, retrying in 10 seconds..."
            sleep 10
          done
          echo "SSH connection timed out."
          exit 1


      - name: Setup Repository on Pod
        uses: appleboy/ssh-action@master
        with:
          host: ${{ steps.get_pod_ip.outputs.pod_ip }}
          username: root
          key: ${{ secrets.SSH_PRIVATE_KEY }} # The key for the action to connect to the pod
          port: ${{ steps.get_pod_ip.outputs.ssh_port }}
          script: |
            # Set up the SSH environment for cloning from GitHub
            mkdir -p ~/.ssh
            echo "${{ secrets.GH_DEPLOY_KEY }}" > ~/.ssh/id_ed25519
            chmod 600 ~/.ssh/id_ed25519
            ssh-keyscan github.com >> ~/.ssh/known_hosts

                
            # Clone the private repository using the deploy key
            git clone "**************:${{ github.repository }}.git" /workspace
            
            # Change to the workspace directory
            cd /workspace

            # install uv
            curl -LsSf https://astral.sh/uv/install.sh | sh
            source $HOME/.local/bin/env

            # install dependencies
            uv sync --extra cuda
