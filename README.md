# runpod-exp-setup

This repository helps set up a development environment on RunPod with GPU support and VSCode remote access.

## Prerequisites

1. Create a RunPod account and get your API key from https://runpod.io/console/user/settings
2. Generate SSH keys for GitHub deployment and RunPod access:
   ```bash
   # Generate GitHub deploy key
   ssh-keygen -t rsa -b 4096 -f gh_deploy_key -N ""
   # Generate SSH key for RunPod access
   ssh-keygen -t rsa -b 4096 -f runpod_ssh_key -N ""
   ```

## Setting up GitHub Secrets

Add the following secrets to your GitHub repository (Settings → Secrets and variables → Actions):

1. `RUNPOD_API_KEY`: Your RunPod API key
2. `GH_DEPLOY_KEY`: Content of `gh_deploy_key` (private key)
3. `SSH_PRIVATE_KEY`: Content of `runpod_ssh_key` (private key)

Also add the public key from `gh_deploy_key.pub` as a deploy key in your repository settings (Settings → Deploy keys). Go to runpod console and add the public key from `runpod_ssh_key.pub` as a SSH key.

## Project Configuration

The pod configuration is stored in `runpod_configs/default.json`. You can create multiple configurations and specify the one to use when running the GitHub action. For available configuration options, refer to the [RunPod API documentation](https://docs.runpod.io/api-reference/pods/POST/pods).

You can install project dependencies by modifying `pyproject.toml` or running `uv add <package>`. And, finaly, you need to add code you want to run on runpod instance. Now, you redy to deploy your code to runpod!

## Deployment

1. Run github action called "Deploy to RunPod"
2. GitHub Actions will automatically:
   - Create a RunPod instance
   - Set up the development environment
   - Install VS Code server

## Connecting from VS Code

1. Install the "Remote - SSH" extension in VS Code
2. Add the following to your SSH config (`~/.ssh/config`):
   ```
   Host runpod
     HostName <POD_IP>  # Get this from RunPod dashboard
     User root
     IdentityFile /path/to/runpod_ssh_key
     Port 22
   ```
3. In VS Code:
   - Press F1 and select "Remote-SSH: Connect to Host..."
   - Choose "runpod"
   - The workspace will be available at `/workspace`
